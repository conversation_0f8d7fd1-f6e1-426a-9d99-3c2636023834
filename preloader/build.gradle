plugins {
    alias(libs.plugins.android.library)
    id 'org.tboox.gradle-xmake-plugin' version '1.2.3'
}

android {
    namespace 'org.levimc.preloader'
    compileSdk 35

    defaultConfig {
        minSdk 24

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"

        ndk {
            //noinspection ChromeOsAbiSupport
            abiFilters 'arm64-v8a', 'armeabi-v7a'
        }
        xmake {
            abiFilters 'arm64-v8a', 'armeabi-v7a'
        }
        externalNativeBuild {
            xmake {
                path "src/main/cpp/preloader/xmake.lua"
                logLevel "verbose"
                buildMode "release"
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    ndkVersion "25.2.9519653"
}

dependencies {
    implementation libs.appcompat
    implementation libs.material
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}