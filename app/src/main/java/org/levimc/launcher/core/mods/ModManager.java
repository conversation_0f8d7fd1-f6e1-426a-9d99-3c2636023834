package org.levimc.launcher.core.mods;

import android.os.FileObserver;

import androidx.lifecycle.MutableLiveData;

import com.google.gson.Gson;

import org.levimc.launcher.core.versions.GameVersion;
import org.levimc.launcher.util.Logger;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;

public class ModManager {
    private static ModManager instance;
    private File modsDir;
    private File configFile;
    private Map<String, Boolean> enabledMap = new LinkedHashMap<>();
    private List<String> modOrder = new ArrayList<>();
    private FileObserver modDirObserver;
    private GameVersion currentVersion;
    private final MutableLiveData<Void> modsChangedLiveData = new MutableLiveData<>();
    private final Gson gson = new Gson();

    private ModManager() {
        if (currentVersion != null && currentVersion.modsDir != null) {
            modsDir = currentVersion.modsDir;
            configFile = new File(modsDir, "mods_config.json");
            if (!modsDir.exists()) modsDir.mkdirs();
            loadConfig();
            initFileObserver();
        } else {
            modsDir = null;
            configFile = null;
        }
    }

    public static synchronized ModManager getInstance() {
        if (instance == null) {
            instance = new ModManager();
        }
        return instance;
    }

    public synchronized void setCurrentVersion(GameVersion version) {
        if (Objects.equals(this.currentVersion, version)) return;
        stopFileObserver();

        this.currentVersion = version;
        if (currentVersion != null) {
            this.modsDir = currentVersion.modsDir;
            if (!modsDir.exists()) modsDir.mkdirs();
            this.configFile = new File(modsDir, "mods_config.json");
            loadConfig();
            initFileObserver();
        } else {
            modsDir = null;
            configFile = null;
            enabledMap = new LinkedHashMap<>();
            modOrder = new ArrayList<>();
        }
        postModChanged();
    }

    public GameVersion getCurrentVersion() {
        return currentVersion;
    }

    public List<Mod> getMods() {
        if (currentVersion == null || modsDir == null) return new ArrayList<>();
        File[] files = modsDir.listFiles((dir, name) -> name.endsWith(".so") || name.endsWith(".zip"));
        List<Mod> mods = new ArrayList<>();
        boolean changed = false;

        if (files != null) {
            for (File file : files) {
                String fileName = file.getName();
                if (!enabledMap.containsKey(fileName)) {
                    enabledMap.put(fileName, true);
                    modOrder.add(fileName);
                    changed = true;
                }
            }
        }

        List<String> toRemove = new ArrayList<>();
        for (String key : enabledMap.keySet()) {
            boolean found = false;
            if (files != null) {
                for (File file : files) {
                    if (file.getName().equals(key)) {
                        found = true;
                        break;
                    }
                }
            }
            if (!found) toRemove.add(key);
        }
        for (String rm : toRemove) {
            enabledMap.remove(rm);
            modOrder.remove(rm);
            changed = true;
        }

        for (int i = 0; i < modOrder.size(); i++) {
            String fileName = modOrder.get(i);
            boolean enabled = Boolean.TRUE.equals(enabledMap.get(fileName));
            Mod mod = createModFromFile(fileName, enabled, i);
            mods.add(mod);
        }

        if (changed) saveConfig();
        return mods;
    }

    private Mod createModFromFile(String fileName, boolean enabled, int order) {
        File modFile = new File(modsDir, fileName);

        if (fileName.endsWith(".zip")) {
            // Handle ZIP mod
            File extractionDir = getExtractionDir();
            String modName = fileName.substring(0, fileName.lastIndexOf('.'));
            File extractedModDir = new File(extractionDir, modName);

            boolean hasAssets = false;
            String extractedPath = null;

            if (extractedModDir.exists()) {
                extractedPath = extractedModDir.getAbsolutePath();
                File assetsDir = new File(extractedModDir, "extracted_assets");
                hasAssets = assetsDir.exists() && assetsDir.listFiles() != null && assetsDir.listFiles().length > 0;
            } else if (enabled) {
                // Extract the ZIP mod if it's enabled but not yet extracted
                extractZipMod(modFile);
                if (extractedModDir.exists()) {
                    extractedPath = extractedModDir.getAbsolutePath();
                    File assetsDir = new File(extractedModDir, "extracted_assets");
                    hasAssets = assetsDir.exists() && assetsDir.listFiles() != null && assetsDir.listFiles().length > 0;
                }
            }

            return new Mod(fileName, enabled, order, hasAssets, extractedPath);
        } else {
            // Handle regular .so mod
            return new Mod(fileName, enabled, order);
        }
    }

    public synchronized void setModEnabled(String fileName, boolean enabled) {
        if (currentVersion == null || modsDir == null) return;
        if (!fileName.endsWith(".so") && !fileName.endsWith(".zip")) {
            fileName += ".so"; // Default to .so for backward compatibility
        }
        if (enabledMap.containsKey(fileName)) {
            enabledMap.put(fileName, enabled);

            // If enabling a ZIP mod, extract it
            if (enabled && fileName.endsWith(".zip")) {
                File modFile = new File(modsDir, fileName);
                extractZipMod(modFile);
            }

            saveConfig();
        }
    }

    private void loadConfig() {
        enabledMap = new LinkedHashMap<>();
        modOrder = new ArrayList<>();
        if (!configFile.exists()) {
            File[] files = modsDir.listFiles((dir, name) -> name.endsWith(".so") || name.endsWith(".zip"));
            if (files != null) {
                for (File file : files) {
                    String fileName = file.getName();
                    enabledMap.put(fileName, true);
                    modOrder.add(fileName);
                }
            }
            saveConfig();
            return;
        }

        try (FileReader reader = new FileReader(configFile)) {
            Type listType = new TypeToken<List<Map<String, Object>>>(){}.getType();
            List<Map<String, Object>> configList = gson.fromJson(reader, listType);
            if (configList != null) {
                for (Map<String, Object> item : configList) {
                    String name = (String) item.get("name");
                    Boolean enabled = (Boolean) item.get("enabled");
                    if (name != null && enabled != null) {
                        enabledMap.put(name, enabled);
                        modOrder.add(name);
                    }
                }
                return;
            }
        } catch (Exception e) {
        }

        try (FileReader reader = new FileReader(configFile)) {
            Type mapType = new TypeToken<Map<String, Boolean>>(){}.getType();
            Map<String, Boolean> oldMap = gson.fromJson(reader, mapType);
            if (oldMap != null) {
                for (Map.Entry<String, Boolean> entry : oldMap.entrySet()) {
                    enabledMap.put(entry.getKey(), entry.getValue());
                    modOrder.add(entry.getKey());
                }
                saveConfig();
            }
        } catch (Exception ignored) {
        }
    }

    private void saveConfig() {
        if (configFile == null) return;
        try (FileWriter writer = new FileWriter(configFile)) {
            List<Map<String, Object>> configList = new ArrayList<>();
            for (int i = 0; i < modOrder.size(); i++) {
                String fileName = modOrder.get(i);
                Map<String, Object> item = new HashMap<>();
                item.put("name", fileName);
                item.put("enabled", enabledMap.get(fileName));
                item.put("order", i);
                configList.add(item);
            }
            gson.toJson(configList, writer);
        } catch (Exception ignored) {
        }
    }

    private synchronized void initFileObserver() {
        if (modsDir == null) return;
        modDirObserver = new FileObserver(modsDir.getAbsolutePath(), FileObserver.CREATE | FileObserver.DELETE | FileObserver.MOVED_FROM | FileObserver.MOVED_TO) {
            @Override
            public void onEvent(int event, String path) {
                postModChanged();
            }
        };
        modDirObserver.startWatching();
    }

    private void stopFileObserver() {
        if (modDirObserver != null) {
            try {
                modDirObserver.stopWatching();
            } catch (Exception ignored) {
            }
            modDirObserver = null;
        }
    }

    public synchronized void deleteMod(String fileName) {
        if (currentVersion == null || modsDir == null) return;
        if (!fileName.endsWith(".so") && !fileName.endsWith(".zip")) {
            fileName += ".so"; // Default to .so for backward compatibility
        }

        File modFile = new File(modsDir, fileName);
        if (modFile.exists()) modFile.delete();

        // If it's a ZIP mod, also clean up extracted files
        if (fileName.endsWith(".zip")) {
            String modName = fileName.substring(0, fileName.lastIndexOf('.'));
            File extractedModDir = new File(getExtractionDir(), modName);
            if (extractedModDir.exists()) {
                ZipModExtractor.deleteDirectory(extractedModDir);
            }
        }

        enabledMap.remove(fileName);
        modOrder.remove(fileName);
        saveConfig();
        postModChanged();
    }

    public synchronized void reorderMods(List<Mod> reorderedMods) {
        if (currentVersion == null || modsDir == null) return;

        modOrder.clear();
        for (Mod mod : reorderedMods) {
            modOrder.add(mod.getFileName());
        }

        saveConfig();
        postModChanged();
    }

    private void postModChanged() {
        modsChangedLiveData.postValue(null);
    }

    public MutableLiveData<Void> getModsChangedLiveData() {
        return modsChangedLiveData;
    }

    public synchronized void refreshMods() {
        postModChanged();
    }

    private File getExtractionDir() {
        if (currentVersion == null) return null;
        File extractionDir = new File(currentVersion.versionDir, "extracted_mods");
        if (!extractionDir.exists()) {
            extractionDir.mkdirs();
        }
        return extractionDir;
    }

    private void extractZipMod(File zipFile) {
        if (!zipFile.exists() || !zipFile.getName().endsWith(".zip")) {
            return;
        }

        File extractionDir = getExtractionDir();
        if (extractionDir == null) {
            Logger.get().error("Cannot get extraction directory");
            return;
        }

        ZipModExtractor.ExtractionResult result = ZipModExtractor.extractZipMod(zipFile, extractionDir);
        if (!result.success) {
            Logger.get().error("Failed to extract ZIP mod: " + zipFile.getName() + " - " + result.errorMessage);
        } else {
            Logger.get().info("Successfully extracted ZIP mod: " + zipFile.getName());
            if (result.hasAssets) {
                Logger.get().info("Mod contains assets: " + zipFile.getName());
            }
            if (result.hasNativeLibs) {
                Logger.get().info("Mod contains native libraries: " + zipFile.getName());
            }
        }
    }

    public void extractAllZipMods() {
        if (currentVersion == null || modsDir == null) return;

        File[] zipFiles = modsDir.listFiles((dir, name) -> name.endsWith(".zip"));
        if (zipFiles != null) {
            for (File zipFile : zipFiles) {
                extractZipMod(zipFile);
            }
        }
    }

    public List<Mod> getEnabledMods() {
        return getMods().stream()
                .filter(Mod::isEnabled)
                .collect(java.util.stream.Collectors.toList());
    }

    public List<Mod> getEnabledZipMods() {
        return getMods().stream()
                .filter(mod -> mod.isEnabled() && mod.getType() == Mod.ModType.ZIP_PACKAGE)
                .collect(java.util.stream.Collectors.toList());
    }

    public List<Mod> getEnabledSoMods() {
        return getMods().stream()
                .filter(mod -> mod.isEnabled() && mod.getType() == Mod.ModType.NATIVE_SO)
                .collect(java.util.stream.Collectors.toList());
    }
}
