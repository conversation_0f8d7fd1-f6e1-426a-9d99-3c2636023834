package org.levimc.launcher.core.mods;

import android.annotation.SuppressLint;

import org.levimc.launcher.util.Logger;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

public class ModNativeLoader {
    @SuppressLint("UnsafeDynamicallyLoadedCode")
    public static void loadEnabledSoMods(ModManager modManager, File cacheDir) {
        List<Mod> mods = modManager.getMods();
        for (Mod mod : mods) {
            if (!mod.isEnabled()) continue;

            if (mod.getType() == Mod.ModType.NATIVE_SO) {
                // Handle traditional .so mods
                loadSoMod(mod, modManager.getCurrentVersion().modsDir, cacheDir);
            } else if (mod.getType() == Mod.ModType.ZIP_PACKAGE) {
                // Handle ZIP package mods
                loadZipMod(mod, cacheDir);
            }
        }
    }

    private static void loadSoMod(Mod mod, File modsDir, File cacheDir) {
        File src = new File(modsDir, mod.getFileName());
        File dir = new File(cacheDir, "mods");
        if (!dir.exists()) dir.mkdirs();
        File dst = new File(dir, mod.getFileName());
        try {
            copyFile(src, dst);
            System.load(dst.getAbsolutePath());
            Logger.get().info("Loaded so: " + dst.getName());
        } catch (IOException | UnsatisfiedLinkError e) {
            Logger.get().error("Can't load " + src.getName() + ": " + e.getMessage());
        }
    }

    private static void loadZipMod(Mod mod, File cacheDir) {
        if (mod.getExtractedPath() == null) {
            Logger.get().error("ZIP mod not extracted: " + mod.getFileName());
            return;
        }

        File extractedDir = new File(mod.getExtractedPath());
        String currentAbi = ZipModExtractor.getCurrentAbi();
        List<File> libraries = ZipModExtractor.findNativeLibrariesForAbi(extractedDir, currentAbi);

        if (libraries.isEmpty()) {
            Logger.get().warn("No native libraries found for ABI " + currentAbi + " in mod: " + mod.getFileName());
            return;
        }

        File modCacheDir = new File(cacheDir, "zip_mods");
        if (!modCacheDir.exists()) modCacheDir.mkdirs();

        for (File library : libraries) {
            try {
                File dst = new File(modCacheDir, library.getName());
                copyFile(library, dst);
                System.load(dst.getAbsolutePath());
                Logger.get().info("Loaded ZIP mod library: " + dst.getName() + " from " + mod.getFileName());
            } catch (IOException | UnsatisfiedLinkError e) {
                Logger.get().error("Can't load library " + library.getName() + " from ZIP mod " + mod.getFileName() + ": " + e.getMessage());
            }
        }
    }

    private static void copyFile(File src, File dst) throws IOException {
        try (InputStream in = new FileInputStream(src);
             OutputStream out = new FileOutputStream(dst)) {
            byte[] buf = new byte[8192];
            int len;
            while ((len = in.read(buf)) > 0) {
                out.write(buf, 0, len);
            }
        }
    }
}
