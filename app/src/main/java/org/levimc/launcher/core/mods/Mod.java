package org.levimc.launcher.core.mods;

public class Mod {
    public enum ModType {
        NATIVE_SO,
        ZIP_PACKAGE
    }

    private final String fileName;
    private boolean enabled;
    private int order;
    private final ModType type;
    private boolean hasAssets;
    private String extractedPath;

    public Mod(String fileName, boolean enabled) {
        this.fileName = fileName;
        this.enabled = enabled;
        this.order = 0;
        this.type = determineModType(fileName);
        this.hasAssets = false;
        this.extractedPath = null;
    }

    public Mod(String fileName, boolean enabled, int order) {
        this.fileName = fileName;
        this.enabled = enabled;
        this.order = order;
        this.type = determineModType(fileName);
        this.hasAssets = false;
        this.extractedPath = null;
    }

    public Mod(String fileName, boolean enabled, int order, boolean hasAssets, String extractedPath) {
        this.fileName = fileName;
        this.enabled = enabled;
        this.order = order;
        this.type = determineModType(fileName);
        this.hasAssets = hasAssets;
        this.extractedPath = extractedPath;
    }

    private ModType determineModType(String fileName) {
        if (fileName.toLowerCase().endsWith(".zip")) {
            return ModType.ZIP_PACKAGE;
        } else if (fileName.toLowerCase().endsWith(".so")) {
            return ModType.NATIVE_SO;
        }
        return ModType.NATIVE_SO; // Default fallback
    }

    public String getFileName() {
        return fileName;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public ModType getType() {
        return type;
    }

    public boolean hasAssets() {
        return hasAssets;
    }

    public void setHasAssets(boolean hasAssets) {
        this.hasAssets = hasAssets;
    }

    public String getExtractedPath() {
        return extractedPath;
    }

    public void setExtractedPath(String extractedPath) {
        this.extractedPath = extractedPath;
    }

    public String getDisplayName() {
        String name = fileName;
        if (name.endsWith(".so")) {
            name = name.replace(".so", "");
        } else if (name.endsWith(".zip")) {
            name = name.replace(".zip", "");
        }
        return name;
    }

    public String getTypeDisplayName() {
        switch (type) {
            case ZIP_PACKAGE:
                return hasAssets ? "ZIP (Native + Assets)" : "ZIP (Native)";
            case NATIVE_SO:
                return "Native Library";
            default:
                return "Unknown";
        }
    }
}