package org.levimc.launcher.core.mods;

import android.os.Build;

import org.levimc.launcher.util.Logger;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

public class ZipModExtractor {
    private static final String TAG = "ZipModExtractor";
    
    public static class ExtractionResult {
        public boolean success;
        public boolean hasNativeLibs;
        public boolean hasAssets;
        public String extractedPath;
        public List<String> extractedLibraries;
        public String errorMessage;
        
        public ExtractionResult() {
            this.success = false;
            this.hasNativeLibs = false;
            this.hasAssets = false;
            this.extractedPath = null;
            this.extractedLibraries = new ArrayList<>();
            this.errorMessage = null;
        }
    }
    
    public static ExtractionResult extractZipMod(File zipFile, File extractionBaseDir) {
        ExtractionResult result = new ExtractionResult();
        
        if (!zipFile.exists() || !zipFile.isFile()) {
            result.errorMessage = "ZIP file does not exist or is not a file";
            return result;
        }
        
        String modName = getModNameFromFile(zipFile);
        File extractionDir = new File(extractionBaseDir, modName);
        
        // Clean existing extraction directory
        if (extractionDir.exists()) {
            deleteDirectory(extractionDir);
        }
        
        if (!extractionDir.mkdirs()) {
            result.errorMessage = "Failed to create extraction directory";
            return result;
        }
        
        try (FileInputStream fis = new FileInputStream(zipFile);
             ZipInputStream zis = new ZipInputStream(new BufferedInputStream(fis))) {
            
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                if (entry.isDirectory()) {
                    File dir = new File(extractionDir, entry.getName());
                    dir.mkdirs();
                    continue;
                }
                
                String entryName = entry.getName();
                File outputFile = new File(extractionDir, entryName);
                
                // Validate entry path to prevent directory traversal
                if (!isValidEntryPath(extractionDir, outputFile)) {
                    Logger.get().warn("Skipping potentially dangerous entry: " + entryName);
                    continue;
                }
                
                // Create parent directories
                File parentDir = outputFile.getParentFile();
                if (parentDir != null && !parentDir.exists()) {
                    parentDir.mkdirs();
                }
                
                // Extract file
                extractFile(zis, outputFile);
                
                // Check what we extracted
                if (entryName.startsWith("libs/") && entryName.endsWith(".so")) {
                    result.hasNativeLibs = true;
                    result.extractedLibraries.add(outputFile.getAbsolutePath());
                } else if (entryName.equals("assets.zip")) {
                    result.hasAssets = true;
                    // Extract assets.zip
                    extractAssetsZip(outputFile, extractionDir);
                }
                
                zis.closeEntry();
            }
            
            result.success = true;
            result.extractedPath = extractionDir.getAbsolutePath();
            
        } catch (IOException e) {
            result.errorMessage = "Failed to extract ZIP: " + e.getMessage();
            Logger.get().error("Failed to extract ZIP mod: " + zipFile.getName(), e);
        }
        
        return result;
    }
    
    private static void extractAssetsZip(File assetsZipFile, File extractionDir) {
        File assetsDir = new File(extractionDir, "extracted_assets");
        if (!assetsDir.mkdirs()) {
            Logger.get().error("Failed to create assets extraction directory");
            return;
        }
        
        try (FileInputStream fis = new FileInputStream(assetsZipFile);
             ZipInputStream zis = new ZipInputStream(new BufferedInputStream(fis))) {
            
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                if (entry.isDirectory()) {
                    File dir = new File(assetsDir, entry.getName());
                    dir.mkdirs();
                    continue;
                }
                
                File outputFile = new File(assetsDir, entry.getName());
                
                // Validate entry path
                if (!isValidEntryPath(assetsDir, outputFile)) {
                    Logger.get().warn("Skipping potentially dangerous assets entry: " + entry.getName());
                    continue;
                }
                
                // Create parent directories
                File parentDir = outputFile.getParentFile();
                if (parentDir != null && !parentDir.exists()) {
                    parentDir.mkdirs();
                }
                
                extractFile(zis, outputFile);
                zis.closeEntry();
            }
            
        } catch (IOException e) {
            Logger.get().error("Failed to extract assets.zip", e);
        }
    }
    
    private static void extractFile(ZipInputStream zis, File outputFile) throws IOException {
        try (BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(outputFile))) {
            byte[] buffer = new byte[8192];
            int len;
            while ((len = zis.read(buffer)) != -1) {
                bos.write(buffer, 0, len);
            }
        }
    }
    
    private static boolean isValidEntryPath(File baseDir, File entryFile) {
        try {
            String basePath = baseDir.getCanonicalPath();
            String entryPath = entryFile.getCanonicalPath();
            return entryPath.startsWith(basePath);
        } catch (IOException e) {
            return false;
        }
    }
    
    private static String getModNameFromFile(File file) {
        String name = file.getName();
        int lastDot = name.lastIndexOf('.');
        return lastDot > 0 ? name.substring(0, lastDot) : name;
    }
    
    public static boolean deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
        }
        return directory.delete();
    }
    
    public static List<File> findNativeLibrariesForAbi(File extractionDir, String abi) {
        List<File> libraries = new ArrayList<>();
        File libsDir = new File(extractionDir, "libs");
        
        if (!libsDir.exists()) {
            return libraries;
        }
        
        File abiDir = new File(libsDir, abi);
        if (!abiDir.exists()) {
            return libraries;
        }
        
        File[] files = abiDir.listFiles((dir, name) -> name.endsWith(".so"));
        if (files != null) {
            for (File file : files) {
                libraries.add(file);
            }
        }
        
        return libraries;
    }
    
    public static String getCurrentAbi() {
        return Build.SUPPORTED_ABIS[0];
    }
    
    public static boolean validateZipModStructure(File zipFile) {
        try (FileInputStream fis = new FileInputStream(zipFile);
             ZipInputStream zis = new ZipInputStream(new BufferedInputStream(fis))) {
            
            boolean hasLibsDir = false;
            boolean hasValidStructure = false;
            
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                String entryName = entry.getName();
                
                if (entryName.startsWith("libs/") && !entry.isDirectory()) {
                    hasLibsDir = true;
                    // Check if it's a valid ABI directory structure
                    String[] parts = entryName.split("/");
                    if (parts.length >= 3 && parts[2].endsWith(".so")) {
                        hasValidStructure = true;
                    }
                }
                
                zis.closeEntry();
            }
            
            return hasLibsDir && hasValidStructure;
            
        } catch (IOException e) {
            Logger.get().error("Failed to validate ZIP mod structure", e);
            return false;
        }
    }
}
