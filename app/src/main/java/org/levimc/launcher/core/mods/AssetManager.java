package org.levimc.launcher.core.mods;

import org.levimc.launcher.core.versions.GameVersion;
import org.levimc.launcher.util.Logger;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

public class AssetManager {
    private static final String TAG = "AssetManager";
    private static AssetManager instance;
    
    private AssetManager() {}
    
    public static synchronized AssetManager getInstance() {
        if (instance == null) {
            instance = new AssetManager();
        }
        return instance;
    }
    
    /**
     * Apply asset overrides from enabled ZIP mods to the game version directory
     */
    public void applyAssetOverrides(GameVersion version, List<Mod> enabledMods) {
        if (version == null || enabledMods == null) {
            return;
        }
        
        Logger.get().info("Applying asset overrides for version: " + version.displayName);
        
        // Clear any existing mod assets
        clearModAssets(version);
        
        // Apply assets from each enabled ZIP mod in order
        for (Mod mod : enabledMods) {
            if (mod.isEnabled() && mod.getType() == Mod.ModType.ZIP_PACKAGE && mod.hasAssets()) {
                applyModAssets(version, mod);
            }
        }
    }
    
    private void applyModAssets(GameVersion version, Mod mod) {
        if (mod.getExtractedPath() == null) {
            Logger.get().warn("Mod " + mod.getFileName() + " has no extracted path");
            return;
        }

        File extractedDir = new File(mod.getExtractedPath());
        File assetsDir = new File(extractedDir, "extracted_assets");

        if (!assetsDir.exists()) {
            Logger.get().warn("No extracted assets found for mod: " + mod.getFileName());
            return;
        }

        try {
            // Minecraft loads assets from the APK file, so we need to modify the APK
            modifyApkWithAssets(version, assetsDir, mod.getFileName());
            Logger.get().info("Applied assets from mod: " + mod.getFileName());
        } catch (IOException e) {
            Logger.get().error("Failed to apply assets from mod: " + mod.getFileName(), e);
        }
    }
    
    private void copyAssetsRecursively(File sourceDir, File targetDir, String modName) throws IOException {
        if (!sourceDir.exists()) {
            return;
        }
        
        File[] files = sourceDir.listFiles();
        if (files == null) {
            return;
        }
        
        for (File file : files) {
            File targetFile = new File(targetDir, file.getName());
            
            if (file.isDirectory()) {
                if (!targetFile.exists()) {
                    targetFile.mkdirs();
                }
                copyAssetsRecursively(file, targetFile, modName);
            } else {
                copyFile(file, targetFile);
                Logger.get().debug("Copied asset: " + file.getName() + " from mod: " + modName);
            }
        }
    }
    
    private void copyFile(File source, File target) throws IOException {
        // Create parent directories if they don't exist
        File parentDir = target.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }
        
        try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(source));
             BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(target))) {
            
            byte[] buffer = new byte[8192];
            int len;
            while ((len = bis.read(buffer)) != -1) {
                bos.write(buffer, 0, len);
            }
        }
    }
    
    private void clearModAssets(GameVersion version) {
        // Restore the original APK from backup if it exists
        File apkFile = new File(version.versionDir, "base.apk.levi");
        File backupApk = new File(version.versionDir, "base.apk.levi.backup");

        if (backupApk.exists() && apkFile.exists()) {
            try {
                copyFile(backupApk, apkFile);
                Logger.get().info("Restored original APK from backup");
            } catch (IOException e) {
                Logger.get().error("Failed to restore APK from backup", e);
            }
        }

        // Clean up any temporary files
        File tempApk = new File(version.versionDir, "base.apk.levi.temp");
        if (tempApk.exists()) {
            tempApk.delete();
        }

        File modAssetsDir = new File(version.versionDir, "mod_assets");
        if (modAssetsDir.exists()) {
            deleteDirectory(modAssetsDir);
        }
    }
    
    private boolean deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
        }
        return directory.delete();
    }
    
    /**
     * Get a list of all asset files that would be overridden by the given mods
     */
    public List<String> getAssetOverrideList(List<Mod> enabledMods) {
        List<String> overrides = new ArrayList<>();
        
        for (Mod mod : enabledMods) {
            if (mod.isEnabled() && mod.getType() == Mod.ModType.ZIP_PACKAGE && mod.hasAssets()) {
                List<String> modAssets = getModAssetList(mod);
                overrides.addAll(modAssets);
            }
        }
        
        return overrides;
    }
    
    private List<String> getModAssetList(Mod mod) {
        List<String> assets = new ArrayList<>();
        
        if (mod.getExtractedPath() == null) {
            return assets;
        }
        
        File extractedDir = new File(mod.getExtractedPath());
        File assetsDir = new File(extractedDir, "extracted_assets");
        
        if (assetsDir.exists()) {
            collectAssetPaths(assetsDir, assetsDir, assets);
        }
        
        return assets;
    }
    
    private void collectAssetPaths(File baseDir, File currentDir, List<String> paths) {
        File[] files = currentDir.listFiles();
        if (files == null) {
            return;
        }
        
        for (File file : files) {
            if (file.isDirectory()) {
                collectAssetPaths(baseDir, file, paths);
            } else {
                try {
                    String relativePath = baseDir.toPath().relativize(file.toPath()).toString();
                    paths.add(relativePath);
                } catch (Exception e) {
                    Logger.get().warn("Failed to get relative path for: " + file.getAbsolutePath());
                }
            }
        }
    }
    
    /**
     * Check if any enabled mods have asset overrides
     */
    public boolean hasAssetOverrides(List<Mod> enabledMods) {
        for (Mod mod : enabledMods) {
            if (mod.isEnabled() && mod.getType() == Mod.ModType.ZIP_PACKAGE && mod.hasAssets()) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Prepare assets for a game launch
     */
    public void prepareAssetsForLaunch(GameVersion version, List<Mod> enabledMods) {
        if (hasAssetOverrides(enabledMods)) {
            Logger.get().info("Preparing asset overrides for game launch");
            applyAssetOverrides(version, enabledMods);
        } else {
            Logger.get().info("No asset overrides to apply");
        }
    }

    /**
     * Modify the APK file to include mod assets
     */
    private void modifyApkWithAssets(GameVersion version, File assetsDir, String modName) throws IOException {
        File apkFile = new File(version.versionDir, "base.apk.levi");
        if (!apkFile.exists()) {
            Logger.get().warn("APK file not found: " + apkFile.getAbsolutePath());
            return;
        }

        // Create a backup of the original APK if it doesn't exist
        File backupApk = new File(version.versionDir, "base.apk.levi.backup");
        if (!backupApk.exists()) {
            copyFile(apkFile, backupApk);
            Logger.get().info("Created APK backup");
        }

        // Create a temporary APK file
        File tempApk = new File(version.versionDir, "base.apk.levi.temp");

        try (ZipInputStream zis = new ZipInputStream(new BufferedInputStream(new FileInputStream(apkFile)));
             ZipOutputStream zos = new ZipOutputStream(new BufferedOutputStream(new FileOutputStream(tempApk)))) {

            // Copy existing entries from the original APK
            ZipEntry entry;
            byte[] buffer = new byte[8192];

            while ((entry = zis.getNextEntry()) != null) {
                // Skip existing assets that might be overridden by mods
                if (entry.getName().startsWith("assets/") && hasAssetOverride(assetsDir, entry.getName())) {
                    Logger.get().debug("Skipping original asset: " + entry.getName() + " (will be replaced by mod)");
                    zis.closeEntry();
                    continue;
                }

                // Copy the entry
                zos.putNextEntry(new ZipEntry(entry.getName()));
                int len;
                while ((len = zis.read(buffer)) > 0) {
                    zos.write(buffer, 0, len);
                }
                zos.closeEntry();
                zis.closeEntry();
            }

            // Add mod assets to the APK
            addAssetsToZip(zos, assetsDir, "assets/");
        }

        // Replace the original APK with the modified one
        if (apkFile.delete()) {
            if (tempApk.renameTo(apkFile)) {
                Logger.get().info("Successfully modified APK with assets from mod: " + modName);
            } else {
                Logger.get().error("Failed to rename temporary APK");
                // Restore from backup
                if (backupApk.exists()) {
                    copyFile(backupApk, apkFile);
                }
            }
        } else {
            Logger.get().error("Failed to delete original APK");
            tempApk.delete();
        }
    }

    private boolean hasAssetOverride(File assetsDir, String assetPath) {
        if (!assetPath.startsWith("assets/")) {
            return false;
        }

        String relativePath = assetPath.substring("assets/".length());
        File assetFile = new File(assetsDir, relativePath);
        return assetFile.exists();
    }

    private void addAssetsToZip(ZipOutputStream zos, File assetsDir, String basePath) throws IOException {
        File[] files = assetsDir.listFiles();
        if (files == null) return;

        for (File file : files) {
            if (file.isDirectory()) {
                addAssetsToZip(zos, file, basePath + file.getName() + "/");
            } else {
                String entryName = basePath + file.getName();
                zos.putNextEntry(new ZipEntry(entryName));

                try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(file))) {
                    byte[] buffer = new byte[8192];
                    int len;
                    while ((len = bis.read(buffer)) > 0) {
                        zos.write(buffer, 0, len);
                    }
                }

                zos.closeEntry();
                Logger.get().debug("Added asset to APK: " + entryName);
            }
        }
    }
}
