package org.levimc.launcher.core.mods;

import org.levimc.launcher.core.versions.GameVersion;
import org.levimc.launcher.util.Logger;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class AssetManager {
    private static final String TAG = "AssetManager";
    private static AssetManager instance;
    
    private AssetManager() {}
    
    public static synchronized AssetManager getInstance() {
        if (instance == null) {
            instance = new AssetManager();
        }
        return instance;
    }
    
    /**
     * Apply asset overrides from enabled ZIP mods to the game version directory
     */
    public void applyAssetOverrides(GameVersion version, List<Mod> enabledMods) {
        if (version == null || enabledMods == null) {
            return;
        }

        Logger.get().info("Applying asset overrides for version: " + version.displayName);

        // Clear any existing mod assets
        clearModAssets(version);

        // Apply assets from each enabled ZIP mod in order using fast asset directory approach
        for (Mod mod : enabledMods) {
            if (mod.isEnabled() && mod.getType() == Mod.ModType.ZIP_PACKAGE && mod.hasAssets()) {
                applyModAssetsFast(version, mod);
            }
        }
    }
    
    private void applyModAssetsFast(GameVersion version, Mod mod) {
        if (mod.getExtractedPath() == null) {
            Logger.get().warn("Mod " + mod.getFileName() + " has no extracted path");
            return;
        }

        File extractedDir = new File(mod.getExtractedPath());
        File assetsDir = new File(extractedDir, "extracted_assets");

        if (!assetsDir.exists()) {
            Logger.get().warn("No extracted assets found for mod: " + mod.getFileName());
            return;
        }

        try {
            // Use fast asset directory approach instead of APK modification
            createAssetOverrideDirectory(version, assetsDir, mod.getFileName());
            Logger.get().info("Applied assets from mod: " + mod.getFileName());
        } catch (IOException e) {
            Logger.get().error("Failed to apply assets from mod: " + mod.getFileName(), e);
        }
    }
    
    private void copyAssetsRecursively(File sourceDir, File targetDir, String modName) throws IOException {
        if (!sourceDir.exists()) {
            return;
        }
        
        File[] files = sourceDir.listFiles();
        if (files == null) {
            return;
        }
        
        for (File file : files) {
            File targetFile = new File(targetDir, file.getName());
            
            if (file.isDirectory()) {
                if (!targetFile.exists()) {
                    targetFile.mkdirs();
                }
                copyAssetsRecursively(file, targetFile, modName);
            } else {
                copyFile(file, targetFile);
                Logger.get().debug("Copied asset: " + file.getName() + " from mod: " + modName);
            }
        }
    }
    
    private void copyFile(File source, File target) throws IOException {
        // Create parent directories if they don't exist
        File parentDir = target.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }
        
        try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(source));
             BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(target))) {
            
            byte[] buffer = new byte[8192];
            int len;
            while ((len = bis.read(buffer)) != -1) {
                bos.write(buffer, 0, len);
            }
        }
    }
    
    private void clearModAssets(GameVersion version) {
        // Simply clear the mod assets directory - much faster than APK restoration
        File modAssetsDir = new File(version.versionDir, "mod_assets");
        if (modAssetsDir.exists()) {
            deleteDirectory(modAssetsDir);
        }

        // Clean up any legacy APK backups and temp files
        File tempApk = new File(version.versionDir, "base.apk.levi.temp");
        if (tempApk.exists()) {
            tempApk.delete();
        }

        File backupApk = new File(version.versionDir, "base.apk.levi.backup");
        if (backupApk.exists()) {
            backupApk.delete();
        }
    }
    
    private boolean deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
        }
        return directory.delete();
    }
    
    /**
     * Get a list of all asset files that would be overridden by the given mods
     */
    public List<String> getAssetOverrideList(List<Mod> enabledMods) {
        List<String> overrides = new ArrayList<>();
        
        for (Mod mod : enabledMods) {
            if (mod.isEnabled() && mod.getType() == Mod.ModType.ZIP_PACKAGE && mod.hasAssets()) {
                List<String> modAssets = getModAssetList(mod);
                overrides.addAll(modAssets);
            }
        }
        
        return overrides;
    }
    
    private List<String> getModAssetList(Mod mod) {
        List<String> assets = new ArrayList<>();
        
        if (mod.getExtractedPath() == null) {
            return assets;
        }
        
        File extractedDir = new File(mod.getExtractedPath());
        File assetsDir = new File(extractedDir, "extracted_assets");
        
        if (assetsDir.exists()) {
            collectAssetPaths(assetsDir, assetsDir, assets);
        }
        
        return assets;
    }
    
    private void collectAssetPaths(File baseDir, File currentDir, List<String> paths) {
        File[] files = currentDir.listFiles();
        if (files == null) {
            return;
        }
        
        for (File file : files) {
            if (file.isDirectory()) {
                collectAssetPaths(baseDir, file, paths);
            } else {
                try {
                    String relativePath = baseDir.toPath().relativize(file.toPath()).toString();
                    paths.add(relativePath);
                } catch (Exception e) {
                    Logger.get().warn("Failed to get relative path for: " + file.getAbsolutePath());
                }
            }
        }
    }
    
    /**
     * Check if any enabled mods have asset overrides
     */
    public boolean hasAssetOverrides(List<Mod> enabledMods) {
        for (Mod mod : enabledMods) {
            if (mod.isEnabled() && mod.getType() == Mod.ModType.ZIP_PACKAGE && mod.hasAssets()) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Prepare assets for a game launch
     */
    public void prepareAssetsForLaunch(GameVersion version, List<Mod> enabledMods) {
        if (hasAssetOverrides(enabledMods)) {
            Logger.get().info("Preparing asset overrides for game launch");
            applyAssetOverrides(version, enabledMods);
        } else {
            Logger.get().info("No asset overrides to apply");
        }
    }

    /**
     * Create asset override directory - much faster than APK modification
     */
    private void createAssetOverrideDirectory(GameVersion version, File assetsDir, String modName) throws IOException {
        File modAssetsDir = new File(version.versionDir, "mod_assets");
        if (!modAssetsDir.exists()) {
            modAssetsDir.mkdirs();
        }

        // Simply copy mod assets to the mod_assets directory
        copyAssetsRecursively(assetsDir, modAssetsDir, modName);

        Logger.get().info("Created asset override directory for mod: " + modName);
    }

}
