<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="48dp"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingVertical="8dp">
    
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:textSize="16sp"/>
        
    <EditText
        android:id="@+id/edit_value"
        android:layout_width="160dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"/>
</LinearLayout>
